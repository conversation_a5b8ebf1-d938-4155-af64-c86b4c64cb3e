/*
Theme Name: <PERSON> Replica
Description: A WordPress theme replicating the design of <PERSON>'s portfolio website
Version: 1.0
Author: Your Name
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #0a0a0a;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 3.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 1.8rem;
}

p {
    font-size: 1.1rem;
    color: #b0b0b0;
    margin-bottom: 1rem;
}

a {
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: #00ff88;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 20px 0;
    transition: all 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-nav a {
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: #00ff88;
    transition: width 0.3s ease;
}

.main-nav a:hover::after {
    width: 100%;
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
}

.hero-content {
    max-width: 800px;
    z-index: 2;
}

.hero-title {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0;
    animation: fadeInUp 1s ease 0.5s forwards;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #00ff88;
    margin-bottom: 2rem;
    opacity: 0;
    animation: fadeInUp 1s ease 0.7s forwards;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0;
    animation: fadeInUp 1s ease 0.9s forwards;
}

.cta-button {
    display: inline-block;
    padding: 15px 30px;
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    color: #000000;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    opacity: 0;
    animation: fadeInUp 1s ease 1.1s forwards;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 255, 136, 0.3);
    color: #000000;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Section Styles */
.section {
    padding: 100px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: #00ff88;
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text {
    font-size: 1.1rem;
    line-height: 1.8;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.skill-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    transition: all 0.3s ease;
}

.skill-item:hover {
    background: rgba(0, 255, 136, 0.1);
    transform: translateY(-5px);
}

/* Portfolio Section */
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.portfolio-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.portfolio-image {
    width: 100%;
    height: 250px;
    background: linear-gradient(45deg, #1a1a1a, #2a2a2a);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

.portfolio-content {
    padding: 1.5rem;
}

.portfolio-title {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.portfolio-description {
    color: #b0b0b0;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.portfolio-tech {
    margin-bottom: 1rem;
}

.portfolio-tech small {
    color: #00ff88;
    font-size: 0.8rem;
}

.portfolio-links {
    display: flex;
    gap: 1rem;
}

.portfolio-link {
    display: inline-block;
    padding: 8px 16px;
    background: rgba(0, 255, 136, 0.1);
    color: #00ff88;
    border: 1px solid #00ff88;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
}

.portfolio-link:hover {
    background: #00ff88;
    color: #000000;
    transform: translateY(-2px);
}

/* Contact Section */
.contact-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.contact-form {
    display: grid;
    gap: 1rem;
    margin-top: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.form-group input,
.form-group textarea {
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: #ffffff;
    font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00ff88;
}

.submit-button {
    padding: 15px 30px;
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    color: #000000;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 255, 136, 0.3);
}

/* Footer */
.site-footer {
    background: #000000;
    padding: 3rem 0 1rem;
    text-align: center;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-link {
    display: inline-block;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #00ff88;
    color: #000000;
    transform: translateY(-3px);
}

/* Animation Classes */
.animate-element {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-element.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Contact Form Messages */
.contact-message {
    padding: 15px;
    margin: 20px 0;
    border-radius: 10px;
    text-align: center;
    font-weight: 500;
}

.contact-message.success {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid #00ff88;
    color: #00ff88;
}

.contact-message.error {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff4444;
    color: #ff4444;
}

/* Enhanced Mobile Menu */
@media (max-width: 768px) {
    .main-nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(10, 10, 10, 0.98);
        backdrop-filter: blur(15px);
        padding: 20px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .main-nav ul {
        flex-direction: column;
        gap: 1rem;
    }

    .main-nav a {
        display: block;
        padding: 10px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-menu-toggle {
        display: block;
        transition: transform 0.3s ease;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .skills-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .container {
        padding: 0 15px;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }
}

/* Loading Animation */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #0a0a0a;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading.fade-out {
    opacity: 0;
    pointer-events: none;
}

.loader {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(0, 255, 136, 0.3);
    border-top: 3px solid #00ff88;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scroll Progress Bar */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, #00ff88, #00cc6a);
    z-index: 1001;
    transition: width 0.1s ease;
}

/* Enhanced Hover Effects */
.skill-item:hover {
    background: rgba(0, 255, 136, 0.1);
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 10px 20px rgba(0, 255, 136, 0.2);
}

.social-link:hover {
    background: #00ff88;
    color: #000000;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 10px 20px rgba(0, 255, 136, 0.3);
}
