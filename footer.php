<!-- Footer -->
<footer class="site-footer">
    <div class="container">
        <div class="social-links">
            <?php
            $social_links = array(
                'email' => get_theme_mod('social_email', ''),
                'linkedin' => get_theme_mod('social_linkedin', ''),
                'twitter' => get_theme_mod('social_twitter', ''),
                'instagram' => get_theme_mod('social_instagram', ''),
                'github' => get_theme_mod('social_github', '')
            );
            
            $social_icons = array(
                'email' => '📧',
                'linkedin' => '💼',
                'twitter' => '🐦',
                'instagram' => '📷',
                'github' => '💻'
            );
            
            foreach ($social_links as $platform => $url) {
                if (!empty($url)) {
                    echo '<a href="' . esc_url($url) . '" class="social-link" target="_blank" rel="noopener noreferrer" aria-label="' . ucfirst($platform) . '">';
                    echo $social_icons[$platform];
                    echo '</a>';
                }
            }
            
            // Default social links if none are set
            if (empty(array_filter($social_links))) {
                echo '<a href="mailto:<EMAIL>" class="social-link" aria-label="Email">📧</a>';
                echo '<a href="#" class="social-link" aria-label="LinkedIn">💼</a>';
                echo '<a href="#" class="social-link" aria-label="Twitter">🐦</a>';
                echo '<a href="#" class="social-link" aria-label="Instagram">📷</a>';
                echo '<a href="#" class="social-link" aria-label="GitHub">💻</a>';
            }
            ?>
        </div>
        <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. All rights reserved.</p>
        <p>Built with WordPress • Designed with ❤️</p>
    </div>
</footer>

<script>
// Initialize loading screen
document.addEventListener('DOMContentLoaded', function() {
    const loading = document.getElementById('loading');
    
    // Hide loading screen after page load
    window.addEventListener('load', function() {
        setTimeout(function() {
            loading.classList.add('fade-out');
            setTimeout(function() {
                loading.style.display = 'none';
            }, 500);
        }, 1000);
    });
    
    // Scroll progress bar
    const scrollProgress = document.getElementById('scroll-progress');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / scrollHeight) * 100;
        
        scrollProgress.style.width = scrollPercent + '%';
    });
    
    // Add smooth reveal animations
    const revealElements = document.querySelectorAll('.section-title, .about-text, .portfolio-item, .skill-item');
    
    const revealObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    revealElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.6s ease';
        revealObserver.observe(el);
    });
    
    // Enhanced portfolio hover effects
    document.querySelectorAll('.portfolio-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-15px) scale(1.03)';
            this.style.boxShadow = '0 25px 50px rgba(0, 255, 136, 0.15)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.3)';
        });
    });
    
    // Parallax effect for hero section
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = scrolled * 0.5;
            heroSection.style.transform = 'translateY(' + parallax + 'px)';
        });
    }
    
    // Add typing cursor effect
    const heroSubtitle = document.querySelector('.hero-subtitle');
    if (heroSubtitle) {
        heroSubtitle.innerHTML += '<span class="typing-cursor">|</span>';
        
        // Animate cursor
        setInterval(function() {
            const cursor = document.querySelector('.typing-cursor');
            if (cursor) {
                cursor.style.opacity = cursor.style.opacity === '0' ? '1' : '0';
            }
        }, 500);
    }
});

// Add custom cursor effect
document.addEventListener('mousemove', function(e) {
    const cursor = document.querySelector('.custom-cursor');
    if (!cursor) {
        const newCursor = document.createElement('div');
        newCursor.className = 'custom-cursor';
        newCursor.style.cssText = `
            position: fixed;
            width: 20px;
            height: 20px;
            background: rgba(0, 255, 136, 0.5);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transition: all 0.1s ease;
            mix-blend-mode: difference;
        `;
        document.body.appendChild(newCursor);
    }
    
    const cursor = document.querySelector('.custom-cursor');
    cursor.style.left = e.clientX - 10 + 'px';
    cursor.style.top = e.clientY - 10 + 'px';
});

// Enhanced form interactions
document.querySelectorAll('.contact-form input, .contact-form textarea').forEach(input => {
    input.addEventListener('focus', function() {
        this.parentNode.classList.add('focused');
    });
    
    input.addEventListener('blur', function() {
        if (!this.value) {
            this.parentNode.classList.remove('focused');
        }
    });
});
</script>

<?php wp_footer(); ?>
</body>
</html>
