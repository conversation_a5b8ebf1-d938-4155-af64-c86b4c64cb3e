<?php get_header(); ?>

<!-- Hero Section -->
<section id="home" class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title"><?php echo get_theme_mod('hero_title', 'Creative Developer'); ?></h1>
            <p class="hero-subtitle"><?php echo get_theme_mod('hero_subtitle', 'Frontend & Interactive Experiences'); ?></p>
            <p class="hero-description">
                <?php echo get_theme_mod('hero_description', 'I create digital experiences that combine beautiful design with cutting-edge technology. Specializing in interactive web applications and creative coding.'); ?>
            </p>
            <a href="#portfolio" class="cta-button">View My Work</a>
        </div>
    </div>
    <div class="hero-background"></div>
</section>

<?php display_contact_messages(); ?>

<!-- About Section -->
<section id="about" class="section">
    <div class="container">
        <h2 class="section-title">About Me</h2>
        <div class="about-content">
            <div class="about-text">
                <p>
                    I'm a passionate creative developer with expertise in modern web technologies. 
                    I love bringing ideas to life through code, creating immersive digital experiences 
                    that engage and inspire users.
                </p>
                <p>
                    With a strong background in both design and development, I bridge the gap between 
                    creative vision and technical implementation. I'm constantly exploring new 
                    technologies and pushing the boundaries of what's possible on the web.
                </p>
                <div class="skills-grid">
                    <div class="skill-item">JavaScript</div>
                    <div class="skill-item">React</div>
                    <div class="skill-item">Three.js</div>
                    <div class="skill-item">WebGL</div>
                    <div class="skill-item">Node.js</div>
                    <div class="skill-item">CSS3</div>
                    <div class="skill-item">HTML5</div>
                    <div class="skill-item">GSAP</div>
                </div>
            </div>
            <div class="about-image">
                <div style="width: 100%; height: 400px; background: linear-gradient(45deg, #1a1a1a, #2a2a2a); border-radius: 15px; display: flex; align-items: center; justify-content: center; color: #666;">
                    Profile Image Placeholder
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Section -->
<section id="portfolio" class="section">
    <div class="container">
        <h2 class="section-title">Featured Work</h2>
        <div class="portfolio-grid">
            <?php
            $portfolio_query = get_portfolio_items(6);

            if ($portfolio_query->have_posts()) :
                while ($portfolio_query->have_posts()) : $portfolio_query->the_post();
                    $project_url = get_post_meta(get_the_ID(), '_project_url', true);
                    $github_url = get_post_meta(get_the_ID(), '_github_url', true);
                    $technologies = get_post_meta(get_the_ID(), '_technologies', true);
            ?>
                <div class="portfolio-item" <?php if ($project_url) echo 'onclick="window.open(\'' . esc_url($project_url) . '\', \'_blank\')"'; ?>>
                    <div class="portfolio-image">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('portfolio-thumb'); ?>
                        <?php else : ?>
                            <div style="width: 100%; height: 250px; background: linear-gradient(45deg, #1a1a1a, #2a2a2a); display: flex; align-items: center; justify-content: center; color: #666;">
                                <?php the_title(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="portfolio-content">
                        <h3 class="portfolio-title"><?php the_title(); ?></h3>
                        <p class="portfolio-description"><?php echo get_the_excerpt(); ?></p>
                        <?php if ($technologies) : ?>
                            <div class="portfolio-tech">
                                <small>Technologies: <?php echo esc_html($technologies); ?></small>
                            </div>
                        <?php endif; ?>
                        <div class="portfolio-links">
                            <?php if ($project_url) : ?>
                                <a href="<?php echo esc_url($project_url); ?>" target="_blank" class="portfolio-link">View Project</a>
                            <?php endif; ?>
                            <?php if ($github_url) : ?>
                                <a href="<?php echo esc_url($github_url); ?>" target="_blank" class="portfolio-link">GitHub</a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php
                endwhile;
                wp_reset_postdata();
            else :
                // Default portfolio items if no custom posts exist
                $default_items = array(
                    array('title' => 'Interactive Web Experience', 'desc' => 'A cutting-edge web application featuring immersive 3D graphics and smooth animations.'),
                    array('title' => 'Creative Portfolio Site', 'desc' => 'Modern portfolio website with custom animations and responsive design.'),
                    array('title' => 'E-commerce Platform', 'desc' => 'Full-stack e-commerce solution with modern UI and seamless user experience.'),
                    array('title' => 'Mobile App Interface', 'desc' => 'Intuitive mobile application design with focus on user experience and accessibility.')
                );

                foreach ($default_items as $item) :
            ?>
                <div class="portfolio-item">
                    <div class="portfolio-image">
                        <div style="width: 100%; height: 250px; background: linear-gradient(45deg, #1a1a1a, #2a2a2a); display: flex; align-items: center; justify-content: center; color: #666;">
                            <?php echo $item['title']; ?>
                        </div>
                    </div>
                    <div class="portfolio-content">
                        <h3 class="portfolio-title"><?php echo $item['title']; ?></h3>
                        <p class="portfolio-description"><?php echo $item['desc']; ?></p>
                    </div>
                </div>
            <?php endforeach; endif; ?>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section id="contact" class="section">
    <div class="container">
        <div class="contact-content">
            <h2 class="section-title">Let's Work Together</h2>
            <p>
                Have a project in mind? I'd love to hear about it. 
                Let's create something amazing together.
            </p>
            <form class="contact-form" method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">
                <input type="hidden" name="action" value="contact_form">
                <?php wp_nonce_field('contact_form_nonce', 'contact_nonce'); ?>
                
                <div class="form-group">
                    <label for="name">Name</label>
                    <input type="text" id="name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="subject">Subject</label>
                    <input type="text" id="subject" name="subject" required>
                </div>
                
                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea id="message" name="message" rows="5" required></textarea>
                </div>
                
                <button type="submit" class="submit-button">Send Message</button>
            </form>
        </div>
    </div>
</section>

<?php get_footer(); ?>
