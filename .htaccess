# Patrick Heng Replica Theme - Performance Optimization

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Leverage browser caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/x-javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType application/pdf "access plus 1 year"
    ExpiresByType application/x-shockwave-flash "access plus 1 year"
</IfModule>

# Add cache headers
<IfModule mod_headers.c>
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|ico|pdf|flv)$">
        Header set Cache-Control "max-age=31536000, public"
    </FilesMatch>
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Disable server signature
ServerSignature Off

# Protect wp-config.php
<Files wp-config.php>
    order allow,deny
    deny from all
</Files>

# Protect .htaccess
<Files .htaccess>
    order allow,deny
    deny from all
</Files>

# Block access to theme files
<FilesMatch "\.(php|phtml)$">
    <RequireAll>
        Require all denied
        Require local
    </RequireAll>
</FilesMatch>

# Allow access to specific theme files
<Files "index.php">
    Require all granted
</Files>

<Files "header.php">
    Require all granted
</Files>

<Files "footer.php">
    Require all granted
</Files>

<Files "functions.php">
    Require all granted
</Files>

<Files "single-portfolio.php">
    Require all granted
</Files>
