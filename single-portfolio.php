<?php get_header(); ?>

<main class="main-content">
    <?php while (have_posts()) : the_post(); ?>
        
        <!-- Portfolio Hero Section -->
        <section class="portfolio-hero section">
            <div class="container">
                <div class="portfolio-hero-content">
                    <h1 class="portfolio-hero-title"><?php the_title(); ?></h1>
                    
                    <?php 
                    $project_url = get_post_meta(get_the_ID(), '_project_url', true);
                    $github_url = get_post_meta(get_the_ID(), '_github_url', true);
                    $technologies = get_post_meta(get_the_ID(), '_technologies', true);
                    ?>
                    
                    <div class="portfolio-meta">
                        <?php if ($technologies) : ?>
                            <div class="portfolio-tech-list">
                                <h3>Technologies Used:</h3>
                                <div class="tech-tags">
                                    <?php 
                                    $tech_array = explode(',', $technologies);
                                    foreach ($tech_array as $tech) :
                                        echo '<span class="tech-tag">' . trim($tech) . '</span>';
                                    endforeach;
                                    ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="portfolio-actions">
                            <?php if ($project_url) : ?>
                                <a href="<?php echo esc_url($project_url); ?>" target="_blank" class="cta-button">View Live Project</a>
                            <?php endif; ?>
                            <?php if ($github_url) : ?>
                                <a href="<?php echo esc_url($github_url); ?>" target="_blank" class="cta-button secondary">View Code</a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <?php if (has_post_thumbnail()) : ?>
                    <div class="portfolio-featured-image">
                        <?php the_post_thumbnail('large'); ?>
                    </div>
                <?php endif; ?>
            </div>
        </section>
        
        <!-- Portfolio Content -->
        <section class="portfolio-content section">
            <div class="container">
                <div class="portfolio-description">
                    <?php the_content(); ?>
                </div>
            </div>
        </section>
        
        <!-- Portfolio Navigation -->
        <section class="portfolio-navigation section">
            <div class="container">
                <div class="portfolio-nav-links">
                    <?php
                    $prev_post = get_previous_post(false, '', 'portfolio');
                    $next_post = get_next_post(false, '', 'portfolio');
                    ?>
                    
                    <?php if ($prev_post) : ?>
                        <div class="nav-link prev-project">
                            <a href="<?php echo get_permalink($prev_post->ID); ?>">
                                <span class="nav-label">Previous Project</span>
                                <span class="nav-title"><?php echo get_the_title($prev_post->ID); ?></span>
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <div class="nav-link back-to-portfolio">
                        <a href="<?php echo home_url('/#portfolio'); ?>">
                            <span class="nav-label">Back to</span>
                            <span class="nav-title">Portfolio</span>
                        </a>
                    </div>
                    
                    <?php if ($next_post) : ?>
                        <div class="nav-link next-project">
                            <a href="<?php echo get_permalink($next_post->ID); ?>">
                                <span class="nav-label">Next Project</span>
                                <span class="nav-title"><?php echo get_the_title($next_post->ID); ?></span>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
        
    <?php endwhile; ?>
</main>

<style>
.portfolio-hero {
    padding-top: 120px;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
}

.portfolio-hero-content {
    text-align: center;
    margin-bottom: 3rem;
}

.portfolio-hero-title {
    font-size: 3rem;
    margin-bottom: 2rem;
    background: linear-gradient(45deg, #ffffff, #00ff88);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.portfolio-meta {
    display: grid;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.portfolio-tech-list h3 {
    color: #00ff88;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.tech-tag {
    background: rgba(0, 255, 136, 0.1);
    color: #00ff88;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    border: 1px solid rgba(0, 255, 136, 0.3);
}

.portfolio-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.cta-button.secondary {
    background: transparent;
    border: 2px solid #00ff88;
    color: #00ff88;
}

.cta-button.secondary:hover {
    background: #00ff88;
    color: #000000;
}

.portfolio-featured-image {
    max-width: 1000px;
    margin: 0 auto;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.portfolio-featured-image img {
    width: 100%;
    height: auto;
    display: block;
}

.portfolio-content {
    max-width: 800px;
    margin: 0 auto;
}

.portfolio-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #b0b0b0;
}

.portfolio-description h2,
.portfolio-description h3 {
    color: #ffffff;
    margin: 2rem 0 1rem;
}

.portfolio-navigation {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.portfolio-nav-links {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    text-align: center;
}

.nav-link.prev-project {
    text-align: left;
}

.nav-link.next-project {
    text-align: right;
}

.nav-link a {
    display: block;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.nav-link a:hover {
    background: rgba(0, 255, 136, 0.1);
    transform: translateY(-2px);
}

.nav-label {
    display: block;
    font-size: 0.9rem;
    color: #00ff88;
    margin-bottom: 0.5rem;
}

.nav-title {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
}

@media (max-width: 768px) {
    .portfolio-hero-title {
        font-size: 2rem;
    }
    
    .portfolio-nav-links {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .nav-link.prev-project,
    .nav-link.next-project {
        text-align: center;
    }
    
    .portfolio-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .tech-tags {
        justify-content: center;
    }
}
</style>

<?php get_footer(); ?>
