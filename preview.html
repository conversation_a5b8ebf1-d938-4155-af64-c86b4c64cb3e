<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Heng Replica - Preview</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>

<!-- Loading Screen -->
<div class="loading" id="loading">
    <div class="loader"></div>
</div>

<!-- Scroll Progress Bar -->
<div class="scroll-progress" id="scroll-progress"></div>

<!-- Header -->
<header class="site-header">
    <div class="container">
        <div class="header-content">
            <div class="logo">
                <a href="#home">Patrick <PERSON></a>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#portfolio">Portfolio</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </nav>
            <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">☰</button>
        </div>
    </div>
</header>

<!-- Hero Section -->
<section id="home" class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">Creative Developer</h1>
            <p class="hero-subtitle">Frontend & Interactive Experiences</p>
            <p class="hero-description">
                I create digital experiences that combine beautiful design with cutting-edge technology. 
                Specializing in interactive web applications and creative coding.
            </p>
            <a href="#portfolio" class="cta-button">View My Work</a>
        </div>
    </div>
    <div class="hero-background"></div>
</section>

<!-- About Section -->
<section id="about" class="section">
    <div class="container">
        <h2 class="section-title">About Me</h2>
        <div class="about-content">
            <div class="about-text">
                <p>
                    I'm a passionate creative developer with expertise in modern web technologies. 
                    I love bringing ideas to life through code, creating immersive digital experiences 
                    that engage and inspire users.
                </p>
                <p>
                    With a strong background in both design and development, I bridge the gap between 
                    creative vision and technical implementation. I'm constantly exploring new 
                    technologies and pushing the boundaries of what's possible on the web.
                </p>
                <div class="skills-grid">
                    <div class="skill-item">JavaScript</div>
                    <div class="skill-item">React</div>
                    <div class="skill-item">Three.js</div>
                    <div class="skill-item">WebGL</div>
                    <div class="skill-item">Node.js</div>
                    <div class="skill-item">CSS3</div>
                    <div class="skill-item">HTML5</div>
                    <div class="skill-item">GSAP</div>
                </div>
            </div>
            <div class="about-image">
                <div style="width: 100%; height: 400px; background: linear-gradient(45deg, #1a1a1a, #2a2a2a); border-radius: 15px; display: flex; align-items: center; justify-content: center; color: #666;">
                    Profile Image Placeholder
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Section -->
<section id="portfolio" class="section">
    <div class="container">
        <h2 class="section-title">Featured Work</h2>
        <div class="portfolio-grid">
            <div class="portfolio-item">
                <div class="portfolio-image">
                    <div style="width: 100%; height: 250px; background: linear-gradient(45deg, #1a1a1a, #2a2a2a); display: flex; align-items: center; justify-content: center; color: #666;">
                        Interactive Web Experience
                    </div>
                </div>
                <div class="portfolio-content">
                    <h3 class="portfolio-title">Interactive Web Experience</h3>
                    <p class="portfolio-description">
                        A cutting-edge web application featuring immersive 3D graphics and smooth animations.
                    </p>
                    <div class="portfolio-tech">
                        <small>Technologies: React, Three.js, WebGL</small>
                    </div>
                    <div class="portfolio-links">
                        <a href="#" class="portfolio-link">View Project</a>
                        <a href="#" class="portfolio-link">GitHub</a>
                    </div>
                </div>
            </div>
            
            <div class="portfolio-item">
                <div class="portfolio-image">
                    <div style="width: 100%; height: 250px; background: linear-gradient(45deg, #1a1a1a, #2a2a2a); display: flex; align-items: center; justify-content: center; color: #666;">
                        Creative Portfolio Site
                    </div>
                </div>
                <div class="portfolio-content">
                    <h3 class="portfolio-title">Creative Portfolio Site</h3>
                    <p class="portfolio-description">
                        Modern portfolio website with custom animations and responsive design.
                    </p>
                    <div class="portfolio-tech">
                        <small>Technologies: HTML5, CSS3, JavaScript</small>
                    </div>
                    <div class="portfolio-links">
                        <a href="#" class="portfolio-link">View Project</a>
                        <a href="#" class="portfolio-link">GitHub</a>
                    </div>
                </div>
            </div>
            
            <div class="portfolio-item">
                <div class="portfolio-image">
                    <div style="width: 100%; height: 250px; background: linear-gradient(45deg, #1a1a1a, #2a2a2a); display: flex; align-items: center; justify-content: center; color: #666;">
                        E-commerce Platform
                    </div>
                </div>
                <div class="portfolio-content">
                    <h3 class="portfolio-title">E-commerce Platform</h3>
                    <p class="portfolio-description">
                        Full-stack e-commerce solution with modern UI and seamless user experience.
                    </p>
                    <div class="portfolio-tech">
                        <small>Technologies: React, Node.js, MongoDB</small>
                    </div>
                    <div class="portfolio-links">
                        <a href="#" class="portfolio-link">View Project</a>
                        <a href="#" class="portfolio-link">GitHub</a>
                    </div>
                </div>
            </div>
            
            <div class="portfolio-item">
                <div class="portfolio-image">
                    <div style="width: 100%; height: 250px; background: linear-gradient(45deg, #1a1a1a, #2a2a2a); display: flex; align-items: center; justify-content: center; color: #666;">
                        Mobile App Interface
                    </div>
                </div>
                <div class="portfolio-content">
                    <h3 class="portfolio-title">Mobile App Interface</h3>
                    <p class="portfolio-description">
                        Intuitive mobile application design with focus on user experience and accessibility.
                    </p>
                    <div class="portfolio-tech">
                        <small>Technologies: React Native, TypeScript</small>
                    </div>
                    <div class="portfolio-links">
                        <a href="#" class="portfolio-link">View Project</a>
                        <a href="#" class="portfolio-link">GitHub</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section id="contact" class="section">
    <div class="container">
        <div class="contact-content">
            <h2 class="section-title">Let's Work Together</h2>
            <p>
                Have a project in mind? I'd love to hear about it. 
                Let's create something amazing together.
            </p>
            <form class="contact-form" onsubmit="handleFormSubmit(event)">
                <div class="form-group">
                    <label for="name">Name</label>
                    <input type="text" id="name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="subject">Subject</label>
                    <input type="text" id="subject" name="subject" required>
                </div>
                
                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea id="message" name="message" rows="5" required></textarea>
                </div>
                
                <button type="submit" class="submit-button">Send Message</button>
            </form>
        </div>
    </div>
</section>

<!-- Footer -->
<footer class="site-footer">
    <div class="container">
        <div class="social-links">
            <a href="mailto:<EMAIL>" class="social-link" aria-label="Email">📧</a>
            <a href="#" class="social-link" aria-label="LinkedIn">💼</a>
            <a href="#" class="social-link" aria-label="Twitter">🐦</a>
            <a href="#" class="social-link" aria-label="Instagram">📷</a>
            <a href="#" class="social-link" aria-label="GitHub">💻</a>
        </div>
        <p>&copy; 2024 Patrick Heng. All rights reserved.</p>
        <p>Built with WordPress • Designed with ❤️</p>
    </div>
</footer>

<script src="js/main.js"></script>
<script>
// Additional preview-specific JavaScript
function handleFormSubmit(event) {
    event.preventDefault();
    alert('This is a preview! In the WordPress version, this form will send real emails.');
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Hide loading screen after a short delay
    setTimeout(function() {
        const loading = document.getElementById('loading');
        loading.classList.add('fade-out');
        setTimeout(function() {
            loading.style.display = 'none';
        }, 500);
    }, 1500);
    
    // Add preview notice
    const notice = document.createElement('div');
    notice.innerHTML = `
        <div style="position: fixed; top: 80px; right: 20px; background: rgba(0, 255, 136, 0.9); color: #000; padding: 10px 15px; border-radius: 5px; z-index: 1000; font-size: 14px; font-weight: 500;">
            🎉 WordPress Theme Preview
        </div>
    `;
    document.body.appendChild(notice);
    
    // Remove notice after 5 seconds
    setTimeout(function() {
        notice.style.opacity = '0';
        setTimeout(function() {
            notice.remove();
        }, 500);
    }, 5000);
});
</script>

</body>
</html>
