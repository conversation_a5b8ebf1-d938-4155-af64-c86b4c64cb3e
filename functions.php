<?php
/**
 * <PERSON> Heng Replica Theme Functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function patrick_heng_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Add custom image sizes
    add_image_size('portfolio-thumb', 400, 300, true);
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'patrick-heng'),
    ));
}
add_action('after_setup_theme', 'patrick_heng_setup');

/**
 * Enqueue Scripts and Styles
 */
function patrick_heng_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style('patrick-heng-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Enqueue custom JavaScript
    wp_enqueue_script('patrick-heng-script', get_template_directory_uri() . '/js/main.js', array(), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('patrick-heng-script', 'ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('patrick_heng_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'patrick_heng_scripts');

/**
 * Handle Contact Form Submission
 */
function handle_contact_form() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['contact_nonce'], 'contact_form_nonce')) {
        wp_die('Security check failed');
    }
    
    // Sanitize form data
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $subject = sanitize_text_field($_POST['subject']);
    $message = sanitize_textarea_field($_POST['message']);
    
    // Validate required fields
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        wp_redirect(home_url('/?contact=error'));
        exit;
    }
    
    // Prepare email
    $to = get_option('admin_email');
    $email_subject = 'Contact Form: ' . $subject;
    $email_message = "Name: $name\n";
    $email_message .= "Email: $email\n";
    $email_message .= "Subject: $subject\n\n";
    $email_message .= "Message:\n$message";
    
    $headers = array(
        'Content-Type: text/plain; charset=UTF-8',
        'From: ' . $name . ' <' . $email . '>',
        'Reply-To: ' . $email
    );
    
    // Send email
    $sent = wp_mail($to, $email_subject, $email_message, $headers);
    
    if ($sent) {
        wp_redirect(home_url('/?contact=success'));
    } else {
        wp_redirect(home_url('/?contact=error'));
    }
    exit;
}
add_action('admin_post_contact_form', 'handle_contact_form');
add_action('admin_post_nopriv_contact_form', 'handle_contact_form');

/**
 * Custom Post Type for Portfolio
 */
function create_portfolio_post_type() {
    $labels = array(
        'name' => 'Portfolio',
        'singular_name' => 'Portfolio Item',
        'menu_name' => 'Portfolio',
        'add_new' => 'Add New',
        'add_new_item' => 'Add New Portfolio Item',
        'edit_item' => 'Edit Portfolio Item',
        'new_item' => 'New Portfolio Item',
        'view_item' => 'View Portfolio Item',
        'search_items' => 'Search Portfolio',
        'not_found' => 'No portfolio items found',
        'not_found_in_trash' => 'No portfolio items found in trash'
    );
    
    $args = array(
        'labels' => $labels,
        'public' => true,
        'has_archive' => true,
        'publicly_queryable' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'portfolio'),
        'capability_type' => 'post',
        'hierarchical' => false,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'menu_icon' => 'dashicons-portfolio',
        'menu_position' => 5,
        'show_in_rest' => true
    );
    
    register_post_type('portfolio', $args);
}
add_action('init', 'create_portfolio_post_type');

/**
 * Add Custom Meta Boxes for Portfolio
 */
function add_portfolio_meta_boxes() {
    add_meta_box(
        'portfolio_details',
        'Portfolio Details',
        'portfolio_details_callback',
        'portfolio',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_portfolio_meta_boxes');

function portfolio_details_callback($post) {
    wp_nonce_field('portfolio_details_nonce', 'portfolio_details_nonce_field');
    
    $project_url = get_post_meta($post->ID, '_project_url', true);
    $github_url = get_post_meta($post->ID, '_github_url', true);
    $technologies = get_post_meta($post->ID, '_technologies', true);
    
    echo '<table class="form-table">';
    echo '<tr>';
    echo '<th><label for="project_url">Project URL</label></th>';
    echo '<td><input type="url" id="project_url" name="project_url" value="' . esc_attr($project_url) . '" style="width: 100%;" /></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="github_url">GitHub URL</label></th>';
    echo '<td><input type="url" id="github_url" name="github_url" value="' . esc_attr($github_url) . '" style="width: 100%;" /></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="technologies">Technologies Used</label></th>';
    echo '<td><input type="text" id="technologies" name="technologies" value="' . esc_attr($technologies) . '" style="width: 100%;" placeholder="e.g., React, Node.js, MongoDB" /></td>';
    echo '</tr>';
    echo '</table>';
}

function save_portfolio_details($post_id) {
    if (!isset($_POST['portfolio_details_nonce_field']) || 
        !wp_verify_nonce($_POST['portfolio_details_nonce_field'], 'portfolio_details_nonce')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    if (isset($_POST['project_url'])) {
        update_post_meta($post_id, '_project_url', sanitize_url($_POST['project_url']));
    }
    
    if (isset($_POST['github_url'])) {
        update_post_meta($post_id, '_github_url', sanitize_url($_POST['github_url']));
    }
    
    if (isset($_POST['technologies'])) {
        update_post_meta($post_id, '_technologies', sanitize_text_field($_POST['technologies']));
    }
}
add_action('save_post', 'save_portfolio_details');

/**
 * Customizer Settings
 */
function patrick_heng_customize_register($wp_customize) {
    // Hero Section
    $wp_customize->add_section('hero_section', array(
        'title' => 'Hero Section',
        'priority' => 30,
    ));
    
    $wp_customize->add_setting('hero_title', array(
        'default' => 'Creative Developer',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('hero_title', array(
        'label' => 'Hero Title',
        'section' => 'hero_section',
        'type' => 'text',
    ));
    
    $wp_customize->add_setting('hero_subtitle', array(
        'default' => 'Frontend & Interactive Experiences',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('hero_subtitle', array(
        'label' => 'Hero Subtitle',
        'section' => 'hero_section',
        'type' => 'text',
    ));
    
    $wp_customize->add_setting('hero_description', array(
        'default' => 'I create digital experiences that combine beautiful design with cutting-edge technology.',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    
    $wp_customize->add_control('hero_description', array(
        'label' => 'Hero Description',
        'section' => 'hero_section',
        'type' => 'textarea',
    ));
    
    // Social Links
    $wp_customize->add_section('social_links', array(
        'title' => 'Social Links',
        'priority' => 35,
    ));
    
    $social_platforms = array('email', 'linkedin', 'twitter', 'instagram', 'github');
    
    foreach ($social_platforms as $platform) {
        $wp_customize->add_setting('social_' . $platform, array(
            'default' => '',
            'sanitize_callback' => 'esc_url_raw',
        ));
        
        $wp_customize->add_control('social_' . $platform, array(
            'label' => ucfirst($platform) . ' URL',
            'section' => 'social_links',
            'type' => 'url',
        ));
    }
}
add_action('customize_register', 'patrick_heng_customize_register');

/**
 * Get Portfolio Items
 */
function get_portfolio_items($limit = -1) {
    $args = array(
        'post_type' => 'portfolio',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'menu_order',
        'order' => 'ASC'
    );
    
    return new WP_Query($args);
}

/**
 * Display Contact Form Messages
 */
function display_contact_messages() {
    if (isset($_GET['contact'])) {
        if ($_GET['contact'] == 'success') {
            echo '<div class="contact-message success">Thank you! Your message has been sent successfully.</div>';
        } elseif ($_GET['contact'] == 'error') {
            echo '<div class="contact-message error">Sorry, there was an error sending your message. Please try again.</div>';
        }
    }
}

/**
 * Remove WordPress version from head
 */
remove_action('wp_head', 'wp_generator');

/**
 * Clean up WordPress head
 */
function patrick_heng_clean_head() {
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
}
add_action('init', 'patrick_heng_clean_head');

/**
 * Optimize for shared hosting
 */
function patrick_heng_optimize_performance() {
    // Remove unnecessary WordPress features for better performance
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('admin_print_styles', 'print_emoji_styles');

    // Disable XML-RPC
    add_filter('xmlrpc_enabled', '__return_false');

    // Remove query strings from static resources
    add_filter('script_loader_src', 'patrick_heng_remove_query_strings', 15, 1);
    add_filter('style_loader_src', 'patrick_heng_remove_query_strings', 15, 1);
}
add_action('init', 'patrick_heng_optimize_performance');

/**
 * Remove query strings from static resources
 */
function patrick_heng_remove_query_strings($src) {
    if (strpos($src, '?ver=')) {
        $src = remove_query_arg('ver', $src);
    }
    return $src;
}

/**
 * Add preload hints for better performance
 */
function patrick_heng_add_preload_hints() {
    echo '<link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
    echo '<noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"></noscript>';
}
add_action('wp_head', 'patrick_heng_add_preload_hints', 1);

/**
 * Defer JavaScript loading
 */
function patrick_heng_defer_scripts($tag, $handle, $src) {
    if (is_admin()) {
        return $tag;
    }

    $defer_scripts = array('patrick-heng-script');

    if (in_array($handle, $defer_scripts)) {
        return '<script src="' . $src . '" defer></script>' . "\n";
    }

    return $tag;
}
add_filter('script_loader_tag', 'patrick_heng_defer_scripts', 10, 3);

/**
 * Add security headers
 */
function patrick_heng_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
    }
}
add_action('send_headers', 'patrick_heng_security_headers');
