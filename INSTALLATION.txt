PATRICK HENG REPLICA WORDPRESS THEME - INSTALLATION GUIDE
========================================================

QUICK START GUIDE FOR SHARED HOSTING
====================================

1. UPLOAD THEME
   - Compress all theme files into a ZIP file
   - In WordPress admin: Appearance > Themes > Add New > Upload Theme
   - Select ZIP file and install
   - Activate the theme

2. BASIC SETUP
   - Go to Appearance > Customize
   - Update Hero Section (title, subtitle, description)
   - Add Social Links (email, LinkedIn, Twitter, etc.)
   - Save changes

3. CREATE PORTFOLIO
   - Go to Portfolio > Add New in WordPress admin
   - Add project title, description, and featured image
   - Fill in Project URL, GitHub URL, and Technologies
   - Publish portfolio items

4. SETUP MENU
   - Go to Appearance > Menus
   - Create menu with: Home (#home), About (#about), Portfolio (#portfolio), Contact (#contact)
   - Assign to "Primary Menu" location

5. TEST CONTACT FORM
   - Visit your website's contact section
   - Send a test message
   - Check if email arrives at your admin email

SHARED HOSTING REQUIREMENTS
===========================

- PHP 7.4 or higher
- WordPress 5.0 or higher
- MySQL 5.6 or higher
- wp_mail() function enabled
- mod_rewrite enabled (for pretty permalinks)

PERFORMANCE TIPS
================

- Enable caching plugin (WP Rocket, W3 Total Cache, etc.)
- Optimize images before uploading
- Use CDN for better loading speeds
- Enable GZIP compression
- Minify CSS/JS if possible

TROUBLESHOOTING
===============

Contact Form Not Working:
- Check if wp_mail() is enabled on your hosting
- Verify admin email in WordPress settings
- Contact hosting provider about email functionality

Animations Not Working:
- Ensure JavaScript is enabled in browser
- Check browser console for errors
- Clear browser cache

Mobile Menu Not Working:
- Clear browser cache
- Check if JavaScript is loading properly
- Ensure theme files uploaded correctly

Theme Not Displaying Correctly:
- Check file permissions (755 for folders, 644 for files)
- Ensure all theme files uploaded
- Clear any caching plugins
- Check WordPress debug log for errors

CUSTOMIZATION
=============

Colors:
- Edit style.css to change color scheme
- Main accent color: #00ff88
- Background: #0a0a0a

Fonts:
- Change Google Fonts link in header.php
- Update font-family in style.css

Layout:
- Modify index.php for structure changes
- Edit style.css for styling changes

SUPPORT
=======

For technical support:
1. Check WordPress documentation
2. Contact your hosting provider
3. Consult with a WordPress developer

Remember to backup your website before making any changes!
