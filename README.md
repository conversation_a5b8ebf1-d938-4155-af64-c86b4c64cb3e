# <PERSON> Heng Replica WordPress Theme

A modern, creative WordPress theme that replicates the design and functionality of <PERSON>'s portfolio website. Perfect for creative developers, designers, and digital professionals.

## Features

- **Modern Design**: Dark theme with smooth animations and interactive elements
- **Responsive**: Fully responsive design that works on all devices
- **Portfolio Management**: Custom post type for managing portfolio projects
- **Contact Form**: Built-in contact form with email functionality
- **Customizable**: WordPress Customizer integration for easy customization
- **Performance Optimized**: Lightweight and fast-loading
- **Shared Hosting Compatible**: Works perfectly on shared hosting environments

## Installation

### Method 1: Upload Theme Files

1. Download or clone this repository
2. Compress the theme folder into a ZIP file
3. In your WordPress admin, go to **Appearance > Themes**
4. Click **Add New** then **Upload Theme**
5. Select your ZIP file and click **Install Now**
6. Activate the theme

### Method 2: FTP Upload

1. Download the theme files
2. Upload the theme folder to `/wp-content/themes/` via FTP
3. In WordPress admin, go to **Appearance > Themes**
4. Find "Patrick Heng Replica" and click **Activate**

## Setup Instructions

### 1. Configure Basic Settings

1. Go to **Appearance > Customize**
2. Update the **Hero Section** settings:
   - Hero Title
   - Hero Subtitle  
   - Hero Description
3. Add your **Social Links**:
   - Email
   - LinkedIn
   - Twitter
   - Instagram
   - GitHub

### 2. Create Portfolio Items

1. In WordPress admin, go to **Portfolio > Add New**
2. Add your project details:
   - Title and description
   - Featured image
   - Project URL
   - GitHub URL
   - Technologies used
3. Publish your portfolio items

### 3. Set Up Navigation Menu

1. Go to **Appearance > Menus**
2. Create a new menu with these items:
   - Home (#home)
   - About (#about)
   - Portfolio (#portfolio)
   - Contact (#contact)
3. Assign the menu to "Primary Menu" location

### 4. Configure Contact Form

The contact form is built-in and will send emails to your WordPress admin email address. Make sure your hosting provider supports the `wp_mail()` function.

## Customization

### Colors

The theme uses CSS custom properties for easy color customization. Main colors:

- Primary: `#00ff88` (Green accent)
- Background: `#0a0a0a` (Dark background)
- Text: `#ffffff` (White text)
- Secondary text: `#b0b0b0` (Gray text)

### Fonts

The theme uses the Inter font family from Google Fonts. You can change this in the `header.php` file.

### Adding Custom CSS

1. Go to **Appearance > Customize > Additional CSS**
2. Add your custom styles

## File Structure

```
patrick-heng-replica/
├── style.css              # Main stylesheet
├── index.php              # Main template
├── header.php             # Header template
├── footer.php             # Footer template
├── functions.php          # Theme functions
├── single-portfolio.php   # Portfolio single page
├── js/
│   └── main.js           # JavaScript functionality
└── README.md             # This file
```

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Performance Features

- Optimized CSS and JavaScript
- Lazy loading for images
- Minified assets
- Efficient animations using CSS transforms
- Intersection Observer for scroll animations

## Shared Hosting Compatibility

This theme is designed to work perfectly on shared hosting environments:

- No server-side dependencies beyond standard WordPress
- Uses standard WordPress functions and hooks
- Optimized for limited server resources
- Compatible with most shared hosting providers

## Support

For support and customization requests, please refer to the WordPress documentation or contact your developer.

## License

This theme is released under the GPL v2 or later license.

## Credits

- Design inspiration: Patrick Heng (https://patrickheng.com/)
- Fonts: Inter by Google Fonts
- Icons: Unicode emoji characters
- Framework: WordPress

## Changelog

### Version 1.0
- Initial release
- Complete portfolio theme with all features
- Responsive design
- Contact form functionality
- Portfolio custom post type
- WordPress Customizer integration
